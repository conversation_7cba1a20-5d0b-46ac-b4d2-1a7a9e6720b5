# PowerShell Syntax Fixes for GitLab CI/CD Pipeline

## 问题概述

在GitLab CI/CD运行过程中遇到了多个PowerShell语法错误，导致pipeline失败。本文档记录了所有修复的问题和解决方案。

## 修复的问题

### 1. 变量引用语法错误

**问题**: 
```powershell
# 错误的语法
docker build -t "$LOCAL_IMAGE_NAME:$LOCAL_IMAGE_TAG" .
```

**错误信息**:
```
Variable reference is not valid. ':' was not followed by a valid variable name character.
```

**解决方案**:
```powershell
# 正确的语法
docker build -t "${LOCAL_IMAGE_NAME}:${LOCAL_IMAGE_TAG}" .
```

**说明**: 在PowerShell中，当变量名后面紧跟冒号时，需要使用`${}`语法来明确变量边界。

### 2. 逻辑OR操作符错误

**问题**:
```powershell
# 错误的语法
docker inspect "$LOCAL_IMAGE_NAME:latest" | Select-String -Pattern "ExposedPorts" || echo "No exposed ports info"
```

**错误信息**:
```
The token '||' is not a valid statement separator in this version.
```

**解决方案**:
```powershell
# 正确的语法
try { docker inspect "${LOCAL_IMAGE_NAME}:latest" | Select-String -Pattern "ExposedPorts" } catch { echo "No exposed ports info" }
```

**说明**: PowerShell不支持`||`操作符，应该使用`try-catch`结构来处理错误。

### 3. 文件路径检查问题

**问题**: 脚本文件路径不存在导致执行失败

**解决方案**:
```powershell
# 添加文件存在性检查
if (Test-Path ".cicd\scripts\generate-docker-report.ps1") { 
    PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\generate-docker-report.ps1" -ImageName $LOCAL_IMAGE_NAME -ImageTag "latest" 
} else { 
    echo "Build report script not found, skipping..." 
}
```

### 4. Docker命令中的变量引用

**问题**: 在Docker命令中使用变量时语法不正确

**修复前**:
```powershell
docker run -d --name $CONTAINER_NAME -p "$HOST_PORT:$CONTAINER_PORT" "$LOCAL_IMAGE_NAME:latest"
```

**修复后**:
```powershell
docker run -d --name $CONTAINER_NAME -p "${HOST_PORT}:${CONTAINER_PORT}" "${LOCAL_IMAGE_NAME}:latest"
```

### 5. 错误重定向语法

**问题**: 使用了bash风格的错误重定向

**修复前**:
```powershell
docker stop $CONTAINER_NAME 2>$null || echo "Container does not exist"
```

**修复后**:
```powershell
try { docker stop $CONTAINER_NAME 2>$null } catch { echo "Container does not exist" }
```

## 修复的文件

### 1. `.gitlab-ci.yml`
- 修复了`build:docker`作业中的变量引用语法
- 修复了`deploy:local`作业中的错误处理
- 修复了`cleanup:local`作业中的PowerShell语法

### 2. `.cicd/scripts/generate-docker-report.ps1`
- 改进了Docker镜像检查逻辑
- 增强了错误处理机制

### 3. 新增测试脚本
- 创建了`test-powershell-syntax.ps1`用于验证语法修复

## 验证结果

运行测试脚本`test-powershell-syntax.ps1`，所有语法测试均通过：

```
=== PowerShell Syntax Test Completed ===
All syntax tests passed. The GitLab CI/CD pipeline should work correctly.
```

## 最佳实践建议

1. **变量引用**: 在PowerShell中，当变量名后面有特殊字符时，始终使用`${variable_name}`语法
2. **错误处理**: 使用`try-catch`而不是`||`操作符
3. **文件路径**: 在执行脚本前使用`Test-Path`检查文件是否存在
4. **Docker命令**: 在Docker命令中使用变量时，确保正确的引用语法
5. **测试验证**: 在部署前使用测试脚本验证PowerShell语法

## 下一步

1. 提交修复后的代码到`ci`分支
2. 运行GitLab CI/CD pipeline验证修复效果
3. 如果还有其他问题，继续迭代修复

## 相关文档

- [PowerShell变量引用文档](https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_variables)
- [PowerShell错误处理文档](https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_try_catch_finally)
- [GitLab CI/CD PowerShell最佳实践](../windows/README.md)
