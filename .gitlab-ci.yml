# Shanghai Hege Technology - GitLab CI/CD Configuration
# Enterprise-grade Next.js application complete CI/CD pipeline
# Supports containerized deployment, security scanning, and automated test reporting

# ==========================================
# 全局配置
# ==========================================

# 定义流水线阶段
stages:
  - prepare      # 环境准备和依赖安装
  - test         # 单元测试和集成测试
  - security     # 安全扫描和代码质量检查
  - build        # 构建应用和Docker镜像
  - deploy       # 部署到目标环境
  - report       # 生成和发布报告

# 全局变量
variables:
  # Node.js 版本
  NODE_VERSION: "18"
  # Docker 镜像仓库
  DOCKER_REGISTRY: "$CI_REGISTRY"
  DOCKER_IMAGE: "$CI_REGISTRY_IMAGE"
  # 应用配置
  APP_NAME: "hege-tech-web"
  APP_PORT: "3000"
  # 缓存配置
  CACHE_KEY: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHA"
  # Windows 环境配置
  WINDOWS_SHELL: "powershell"

# 全局缓存配置
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .next/cache/
  policy: pull-push

# Windows 环境默认配置
default:
  tags:
    - windows
    - shell
  before_script:
    - PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\setup-powershell-environment.ps1"
    - echo "Starting CI/CD Pipeline - $(Get-Date)"
    - echo "Project - $env:APP_NAME"
    - echo "Branch - $env:CI_COMMIT_REF_NAME"
    - echo "Commit - $env:CI_COMMIT_SHORT_SHA"
    - echo "Configuring npm registry..."
    - npm config set registry https://registry.npmmirror.com/

# ==========================================
# 准备阶段 - 环境准备和依赖安装
# ==========================================

# Windows Runner 测试作业
test:windows-runner:
  stage: prepare
  script:
    - echo "Testing Windows Runner connection..."
    - echo "Current time - $(Get-Date)"
    - echo "PowerShell version - $($PSVersionTable.PSVersion)"
    - echo "Current directory - $(Get-Location)"
    - echo "Environment variable CI_PROJECT_NAME - $env:CI_PROJECT_NAME"
    - echo "Windows Runner test completed successfully"
  only:
    - ci
    - main
    - develop

prepare:dependencies:
  stage: prepare
  script:
    - echo "Installing dependencies using PowerShell script..."
    - PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\install-dependencies.ps1"
    - echo "Dependencies installation completed"
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
      - .npm/
    policy: push
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  only:
    changes:
      - package.json
      - package-lock.json
      - "**/*.js"
      - "**/*.ts"
      - "**/*.tsx"

# ==========================================
# 测试阶段 - 单元测试和集成测试
# ==========================================

test:unit:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - echo "Running unit tests..."
    - npm run test:ci
    - echo "Generating test coverage report..."
    - npm run test:coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    when: always
    reports:
      junit: coverage/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

test:lint:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - echo "Running code linting..."
    - npm run lint
    - echo "Checking code format..."
    - npm run format:check
  artifacts:
    when: on_failure
    paths:
      - lint-results.json
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

test:type-check:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - echo "Running TypeScript type checking..."
    - npm run type-check
  only:
    - main
    - develop
    - merge_requests

# ==========================================
# 安全扫描阶段
# ==========================================

security:sast:
  stage: security
  dependencies:
    - prepare:dependencies
  script:
    - echo "Running Static Application Security Testing (SAST)..."
    - npm audit --audit-level=moderate
    - echo "Running ESLint security rules check..."
    - npx eslint . --config .cicd/security/sast/.eslintrc.security.js --format json --output-file sast-results.json || true
  artifacts:
    when: always
    reports:
      sast: sast-results.json
    paths:
      - sast-results.json
    expire_in: 1 week
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

security:dependency-scan:
  stage: security
  dependencies:
    - prepare:dependencies
  script:
    - echo "Running dependency vulnerability scan..."
    - npm audit --json > dependency-scan.json || true
    - echo "Generating dependency report..."
    - npm ls --depth=0 > dependencies-list.txt
  artifacts:
    when: always
    reports:
      dependency_scanning: dependency-scan.json
    paths:
      - dependency-scan.json
      - dependencies-list.txt
    expire_in: 1 week
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

# ==========================================
# 构建阶段
# ==========================================

build:app:
  stage: build
  dependencies:
    - prepare:dependencies
  script:
    - echo "Building Next.js application..."
    - npm run build
    - echo "Build completed, static files generated"
    - Get-ChildItem .next\ -Force | Format-Table Name, Length, LastWriteTime
  artifacts:
    paths:
      - .next/
      - out/
    expire_in: 1 day
  only:
    - main
    - develop
    - merge_requests

build:docker:
  stage: build
  dependencies:
    - build:app
  variables:
    LOCAL_IMAGE_NAME: "hege-tech-web"
    LOCAL_IMAGE_TAG: "$CI_COMMIT_SHORT_SHA"
  before_script:
    - echo "Checking Docker environment..."
    - docker --version
    - docker info
    - echo "Current Docker images list"
    - docker images
  script:
    - echo "Building Docker image locally..."
    - docker build -f .cicd/docker/Dockerfile -t "${LOCAL_IMAGE_NAME}:${LOCAL_IMAGE_TAG}" -t "${LOCAL_IMAGE_NAME}:latest" .
    - echo "Docker image build completed"
    - echo "Post-build images list"
    - docker images $LOCAL_IMAGE_NAME
    - echo "Image details"
    - docker inspect "${LOCAL_IMAGE_NAME}:latest" | Select-String -Pattern "ExposedPorts"; if ($LASTEXITCODE -ne 0) { echo "No exposed ports info" }
    - docker inspect "${LOCAL_IMAGE_NAME}:latest" | Select-String -Pattern "Env" | Select-Object -First 5; if ($LASTEXITCODE -ne 0) { echo "No environment variables info" }
    - echo "Generating build report..."
    - if (Test-Path ".cicd\scripts\generate-docker-report.ps1") { PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\generate-docker-report.ps1" -ImageName $LOCAL_IMAGE_NAME -ImageTag "latest" } else { echo "Build report script not found, skipping..." }
  artifacts:
    reports:
      junit: docker-build-report.xml
    paths:
      - docker-build-report.xml
    expire_in: 1 day
    when: always
  only:
    - main
    - develop
    - ci

# ==========================================
# 部署阶段
# ==========================================

deploy:local:
  stage: deploy
  dependencies:
    - build:docker
  variables:
    LOCAL_IMAGE_NAME: "hege-tech-web"
    CONTAINER_NAME: "hege-tech-web-local"
    CONTAINER_PORT: "3000"
    HOST_PORT: "3000"
  environment:
    name: local
    url: http://localhost:3000
  script:
    - echo "Deploying to local Docker environment..."
    - PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\deploy-local-docker.ps1" -ImageName $LOCAL_IMAGE_NAME -ImageTag "latest" -ContainerName $CONTAINER_NAME -HostPort $HOST_PORT -ContainerPort $CONTAINER_PORT
  after_script:
    - echo "Post-deployment status check..."
    - docker ps -a -f name=$CONTAINER_NAME --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    - echo "Latest container logs (last 10 lines)"
    - PowerShell.exe -Command "docker logs --tail 10 $env:CONTAINER_NAME 2>`$null; if (`$LASTEXITCODE -ne 0) { Write-Host 'Unable to retrieve logs' }"
  only:
    - main
    - develop
    - ci
  when: manual

cleanup:local:
  stage: deploy
  script:
    - echo "Cleaning up local Docker environment..."
    - PowerShell.exe -ExecutionPolicy Bypass -File ".cicd\scripts\cleanup-local-docker.ps1" -ImageNameFilter "hege-tech-web"
  only:
    - main
    - develop
    - ci
  when: manual